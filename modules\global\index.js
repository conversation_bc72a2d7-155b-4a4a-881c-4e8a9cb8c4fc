const express = require("express");
const router = express.Router();

const globalMastersRoutes = require("./globalMasters/routes");

const asharaGuideRoutes = require("./asharaGuide/routes");
const accomodationRoutes = require("./muqimeenMehmaanMapping/routes");
const documentRoutes = require("./documentManager/routes");
const reports = require("./reports/routes");
const analyticsLogRoute = require('./analyticsLog/routes');

router.use("/global-master", globalMastersRoutes);
router.use("/ashara-guide", asharaGuideRoutes);
router.use("/accomodation", accomodationRoutes);
router.use("/document-manager", documentRoutes);
router.use("/report", reports);
router.use('/activity', analyticsLogRoute);


module.exports = router;
