const express = require("express");
const router = express.Router();
const {
  getArazCity,
  getCompileList,
  getArazCityHrDashboardReport,
  saveArazCityHrDashboardReport,
} = require("../controllers/arazCityHRDashboard.controller");
const {
  getArazCitySchema,
  getArazCityHrDashboardReportSchema,
  saveArazCityHrDashboardReportSchema,
} = require("../validations/arazCityHRDashboard.validation");
const { validate } = require("../../../../middlewares/validation.middleware");

router.get(
  "/get/araz-city/:miqaatID",
  validate(getArazCitySchema, "params"),
  getArazCity
);

router.get(
  "/get/compile-list",
  getCompileList
);

router.post(
  "/get/report",
  validate(getArazCityHrDashboardReportSchema, "body"),
  getArazCityHrDashboardReport
);

router.post(
  "/add/report",
  validate(saveArazCityHrDashboardReportSchema, "body"),
  saveArazCityHrDashboardReport
);

module.exports = router;
