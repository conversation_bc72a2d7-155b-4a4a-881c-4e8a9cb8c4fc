const { upload } = require("../../../../middlewares/multer.middleware");
const { validate } = require("../../../../middlewares/validation.middleware");
const { addHotel, getSingleHotel, updateHotel, deleteHotel, uploadHotelImage, getDownloadURL, getAllHotels } = require("../controllers/hotel.controller");
const { addHotelSchema, getHotelsSchema, getSingleHotelSchema, updateHotelSchema, deleteHotelSchema, getDownloadURLSchema } = require("../validations/hotel.validation");
const router = require("express").Router();

const fileUpload = upload("hotel_images");

router.post(
  "/upload",
  fileUpload.single("image"),
  uploadHotelImage
);
router.post(
  "/get/image/download-url",
  validate(getDownloadURLSchema, "body"),
  getDownloadURL
);
router.post("/add", validate(addHotelSchema, "body"), addHotel);
router.get("/get", validate(getHotelsSchema, "query"), getAllHotels);
router.get("/get/:id", validate(getSingleHotelSchema, "params"), getSingleHotel);
router.put("/edit", validate(updateHotelSchema, "body"), updateHotel);
router.delete("/delete/:id", validate(deleteHotelSchema, "params"), deleteHotel);
module.exports = router;
