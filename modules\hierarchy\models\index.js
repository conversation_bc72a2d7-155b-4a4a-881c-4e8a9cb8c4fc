const { ArazCity } = require("../../global/globalMasters/models/arazCity.model");
const { Miqaat } = require("../../global/globalMasters/models/miqaat.model");
const { ArazCityZone } = require("./arazCityZone.model");
const { ArazCityZoneFile } = require("./arazCityZoneFiles.model");
const { Department } = require("../../global/globalMasters/models/department.model");
const { Jamiat, Jamaat } = require("./jamiatJamaat.model");
const { KGType } = require("../../global/globalMasters/models/kgType.model");
const { KGGroup } = require("../../global/globalMasters/models/kgGroup.model");
const { HierarchyPosition } = require("../../global/globalMasters/models/hierarchyPosition.model");
const { Hierarchy } = require("./hierarchy.model");
const { KGUser } = require("./kgUser.model");
const { SystemRole } = require("../../global/globalMasters/models/systemRole.model");
const { Function } = require("../../global/globalMasters/models/function.model");
const { PositionAssignment } = require("./positionAssignment.model");
const { TravelArazCity } = require("./travelArazCity.model");
const { TravelPriority } = require("./travelPriority.model");
const { RazaRecommendation } = require("./razaRecommendation.model");
const { CompileList } = require("./compileList.model");
const { RazaList } = require("./razaList.model");
const { KgRequisition } = require("./kgRequistion.model");
const { Interest } = require("./interest.model");
const { KgRequisitionByDepartment } = require("./kgRequisitionByDepartment.model");
const constants = require("../../../constants");
const {
  populateDefaultEntries,
  addDefaultEntries,
} = require("../../../utils/schema.util");
const { Attendance } = require("./attendance.model");

// populate default entries
const insertDefaultEntries = async () => {
  const defaultEntriesToBeCreated = [
    // [KGGroup, "KG_GROUPS"],
    // [KGType, "KG_TYPES"],
    // [SystemRole, "SYSTEM_ROLES"],
    // [ArazCityZone, "ARAZ_CITY_ZONES"],
    // [HierarchyPosition, "HIERARCHY_POSITIONS"],
    // [Department, "DEPARTMENTS"],
  ];

  for (const [model, type] of defaultEntriesToBeCreated) {
    const defaultEntries = populateDefaultEntries(
      constants[type],
      model.modelName
    );

    await addDefaultEntries(model, defaultEntries);
  }
};
// NOT TO UNCOMMENT THIS LINE AS IT WILL DISRPUT THE CODE
// ONLY DO SO IN CASE OF HIERARCHY POSITIONS AND DEPARTMENTS
// DELETD FROM DB
// (async function() {await insertDefaultEntries()})()

module.exports = {
  ArazCity,
  ArazCityZone,
  Department,
  Miqaat,
  Jamiat,
  Jamaat,
  KGType,
  KGGroup,
  HierarchyPosition,
  Hierarchy,
  KGUser,
  SystemRole,
  Function,
  PositionAssignment,
  KgRequisition,
  ArazCityZoneFile,
  Interest,
  TravelArazCity,
  TravelPriority,
  RazaRecommendation,
  CompileList,
  RazaList,
  KgRequisitionByDepartment,
  Attendance,
};
