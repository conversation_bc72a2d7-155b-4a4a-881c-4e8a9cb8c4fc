const { Schema, model } = require("mongoose");

const waazVenueSuitabilitySchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      unique: true,
    },
    uniqueName: {
      type: String,
      required: true,
      trim: true,
      unique: true,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

const WaazVenueSuitability = model(
  "WaazVenueSuitability",
  waazVenueSuitabilitySchema
);

module.exports = { WaazVenueSuitability };
