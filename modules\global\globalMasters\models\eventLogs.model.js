const { Schema, model } = require("mongoose");

const eventLogSchema = new Schema(
  {
    action: {
      type: String,
      required: true,
    },
    module: {
      type: String,
      required: true,
    },
    actionOn: {
      // miqaat, arazCity
      type: String,
      required: true,
    },
    message: {
      type: String,
      required: true,
    },
    performedBy: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
      required: true,
    },
    performedAt: {
      type: Date,
      default: Date.now,
    },
  },
  { timestamps: true }
);

const EventLogs = model("EventLogs", eventLogSchema);

module.exports = {EventLogs};
