const { Schema, model } = require("mongoose");

const mawaidVenueTypeSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      unique: true,
    },
    uniqueName: {
      type: String,
      required: true,
      trim: true,
      unique: true,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

const MawaidVenueType = model("MawaidVenueType", mawaidVenueTypeSchema);

module.exports = { MawaidVenueType };
