const Joi = require("joi");
const {
  stringValidation,
  booleanValidation,
  numberValidation,
  urlValidation,
  dateValidation,
  arrayObjectValidation,
  idValidation,
} = require("../../../../utils/validator.util");

const addDepartmentSchema = Joi.object({
  name: stringValidation,
  LDName: stringValidation.optional().allow(""),
  isZonal: booleanValidation,
  allowDeactivate: booleanValidation,
  status: stringValidation,
  hierarchyPositions: Joi.array().items(idValidation).optional(),
}).unknown();

const getSingleDepartmentSchema = Joi.object({
  id: idValidation,
});

const editDepartmentSchema = Joi.object({
  id: idValidation,
  name: stringValidation,
  LDName: stringValidation.optional().allow(""),
  isZonal: booleanValidation,
  allowDeactivate: booleanValidation,
  status: stringValidation,
  hierarchyPositions: Joi.array().items(idValidation).optional(),
}).unknown();

const deleteDepartmentSchema = Joi.object({
  id: idValidation,
});

const updateDepartmentStatusSchema = Joi.object({
  status: stringValidation.valid("active", "inactive"),
  arazCityID: idValidation,
  departmentIDs: Joi.array().items(idValidation.optional()).optional(),
});

const updateDepartmentKhidmatStatusSchema = Joi.object({
  showForKhidmatInterest: booleanValidation,
  arazCityID: idValidation,
  departmentID:idValidation,
});

const getDepartmentReportSchema = Joi.object({
  miqaatID: idValidation,
  arazCityID: idValidation,
  departmentID: idValidation,
  kgTypes: Joi.array().items(stringValidation).optional(),
});

const getDepartmentByArazCitySchema = Joi.object({
  id: idValidation,
});

const getHODReportSchema = Joi.object({
  kgTypes: Joi.array().items(stringValidation).optional(),
  miqaatID: idValidation,
  arazCityID: idValidation
});


const addEditDepartmentQuotaSchema = Joi.object({
  departments: Joi.array().items({departmentID: idValidation, quota: numberValidation}).required(),
});

const getDepartmentQuotaSchema = Joi.object({
  departmentID: Joi.array().items(idValidation).required(),
})

module.exports = {
  addDepartmentSchema,
  getSingleDepartmentSchema,
  editDepartmentSchema,
  deleteDepartmentSchema,
  updateDepartmentStatusSchema,
  getDepartmentReportSchema,
  getDepartmentByArazCitySchema,
  getHODReportSchema,
  addEditDepartmentQuotaSchema,
  getDepartmentQuotaSchema,
  updateDepartmentKhidmatStatusSchema
};
