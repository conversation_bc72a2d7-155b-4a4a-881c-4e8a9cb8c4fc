const {
  getAllGuides,
  getDownloadURL,
} = require("./../../globalMasters/controllers/guide.controller");
const { getDownloadURLSchema } = require("./../../globalMasters/validations/guide.validation");
const { validate } = require("../../../../middlewares/validation.middleware");

const router = require("express").Router();

router.get("/get/all", getAllGuides);

router.post(
  "/get/download-url",
  validate(getDownloadURLSchema, "body"),
  getDownloadURL
);

module.exports = router;
