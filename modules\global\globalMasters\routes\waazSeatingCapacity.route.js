const {
  getAllWaazSeatingCapacities,
  addEditWaazSeatingCapacity,
  getSingleWaazSeatingCapacity,
  deleteWaazSeatingCapacity,
  getMultiplicationFactor,
} = require("../controllers/waazSeatingCapacity.Controller");
const {
  addWaazSeatingCapacitySchema,
  getSingleWaazSeatingCapacitySchema,
  editWaazSeatingCapacitySchema,
  deleteWaazSeatingCapacitySchema,
  getMultiplicationFactorSchema,
} = require("../validations/waazSeatingCapacity.validation");
const { validate } = require("../../../../middlewares/validation.middleware");

const router = require("express").Router();

router.get("/get", getAllWaazSeatingCapacities);
router.get(
  "/get/multiplication-factor",
  validate(getMultiplicationFactorSchema, "query"),
  getMultiplicationFactor
);

router.post(
  "/add",
  validate(addWaazSeatingCapacitySchema, "body"),
  addEditWaazSeatingCapacity
);

router.get(
  "/get/:id",
  validate(getSingleWaazSeatingCapacitySchema, "params"),
  getSingleWaazSeatingCapacity
);

router.put(
  "/edit",
  validate(editWaazSeatingCapacitySchema, "body"),
  addEditWaazSeatingCapacity
);

router.delete(
  "/delete/:id",
  validate(deleteWaazSeatingCapacitySchema, "params"),
  deleteWaazSeatingCapacity
);

module.exports = router;
