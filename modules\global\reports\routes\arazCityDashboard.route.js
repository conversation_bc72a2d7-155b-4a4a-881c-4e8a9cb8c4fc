const { validate } = require("../../../../middlewares/validation.middleware");
const { getUsersSummaryReport, getUsersAnalyticsReport, getKGPoolInterestDept, getPermissionWiseUser, getOverallUsersReport } = require("../controllers/arazCityDashboard.controller");
const { getKGArrivalInfo } = require("../controllers/KGArrivalInfo.controller");
const { getUsersReportSchema } = require("../validations/arazCityDashboard.validation");

const router = require("express").Router();

router.get("/get/venue-summary", validate(getUsersReportSchema, "query"), getUsersSummaryReport);
router.get("/get/user-analytics", validate(getUsersReportSchema, "query"), getUsersAnalyticsReport);
router.get("/get/interest-position-overview", validate(getUsersReportSchema, "query"), getKGPoolInterestDept);
router.get("/get/permission-wise-user", validate(getUsersReportSchema, "query"), getPermissionWiseUser);
router.get("/get/overall-report", getOverallUsersReport);
router.get("/get/arrival-info", validate(getUsersReportSchema, "query"), getKGArrivalInfo);

module.exports = router;
