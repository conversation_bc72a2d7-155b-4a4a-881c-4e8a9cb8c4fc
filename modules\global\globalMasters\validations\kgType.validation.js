const Joi = require("joi");
const {
  stringValidation,
  booleanValidation,
  numberValidation,
  urlValidation,
  dateValidation,
  arrayObjectValidation,
  idValidation,
  emailValidation,
} = require("../../../../utils/validator.util");

const addKGTypeSchema = Joi.object({
  name: stringValidation,
}).unknown();

const getSingleKGTypeSchema = Joi.object({
  id: idValidation,
});

const editKGTypeSchema = Joi.object({
  id: idValidation,
  name: stringValidation,
}).unknown();

const deleteKGTypeSchema = Joi.object({
  id: idValidation,
});

module.exports = {
  addKGTypeSchema,
  getSingleKGTypeSchema,
  editKGTypeSchema,
  deleteKGTypeSchema,
};
