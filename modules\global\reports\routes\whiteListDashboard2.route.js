const express = require("express");
const router = express.Router();
const { validate } = require("../../../../middlewares/validation.middleware");
const { getWhiteListDashboard2Schema } = require("../validations/whiteListDashboard2.validation");
const { getWhiteListDashboard2 } = require("../controllers/whiteListDashboard2.controller");
const { getCompileList } = require("../controllers/arazCityHRDashboard.controller");
const { getAllDepartments } = require("../../globalMasters/controllers/department.controller");

router.post("/get/report", validate(getWhiteListDashboard2Schema, "body"), getWhiteListDashboard2);
router.get("/get/compile-list", getCompileList);
router.get("/get/department",getAllDepartments);

module.exports = router;

