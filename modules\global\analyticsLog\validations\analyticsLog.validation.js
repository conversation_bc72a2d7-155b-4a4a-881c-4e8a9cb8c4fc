const Joi = require("joi");
const {
  idValidation,
  stringValidation,
} = require("../../../../utils/validator.util");
const { userAnalyticsAction, deviceType } = require("../models/analyticsLog.model");

const addAnalyticsLogSchema = Joi.object({
  action: stringValidation.valid(...Object.values(userAnalyticsAction)).optional().allow(""),
  module: stringValidation.optional().allow(""),
  page: stringValidation.optional().allow(""),
  url: stringValidation.optional().allow(""),
  deviceType: stringValidation.valid(...Object.values(deviceType)).optional().allow(""),
  browser: stringValidation.optional().allow(""),
  os: stringValidation.optional().allow(""),
  IP: stringValidation.optional().allow(""),
  location: Joi.object({
    city: stringValidation.optional().allow(""),
    state: stringValidation.optional().allow(""),
    country: stringValidation.optional().allow("")
  }).optional(),
  miqaatID: idValidation,
  arazCityID: idValidation,
});

const getUserAnalyticsSchema = Joi.object({
  miqaatID: idValidation.optional().allow(""),
  arazCityID: idValidation.optional().allow(""),
});

module.exports = {
  addAnalyticsLogSchema,
  getUserAnalyticsSchema
};
