const express = require("express");
const router = express.Router();
const { validate } = require("../../../middlewares/validation.middleware");

// Import controllers
const { getProfile, markAttendance } = require("../controllers/attendance.controller");

// Import validations
const { getProfileSchema, markAttendanceSchema } = require("../validations/attendance.validation");

// Attendance routes
router.post("/get/profile", validate(getProfileSchema, "body"), getProfile);
router.post("/add", validate(markAttendanceSchema, "body"), markAttendance);

module.exports = router;
