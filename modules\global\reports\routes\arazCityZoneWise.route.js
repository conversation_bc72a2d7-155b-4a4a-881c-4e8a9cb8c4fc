const { validate } = require("../../../../middlewares/validation.middleware");
const { getArazCity, getCompileList } = require("../controllers/arazCityHRDashboard.controller");
const { getArazCityZoneWiseReport } = require("../controllers/arazCityZoneWise.controller");
const { getArazCitySchema } = require("../validations/arazCityHRDashboard.validation");
const { getArazCityZoneWiseReportSchema } = require("../validations/arazCityZoneWise.validation");

const router = require("express").Router();
router.get(
  "/get/araz-city/:miqaatID",
  validate(getArazCitySchema, "params"),
  getArazCity
);

router.get(
  "/get/compile-list",
  getCompileList
);

router.post("/get/report", validate(getArazCityZoneWiseReportSchema, "body"),  getArazCityZoneWiseReport);

module.exports = router;

