const { validate } = require("../../../../middlewares/validation.middleware");

const {
  getAllDepartments,
  addDepartment,
  getSingleDepartment,
  editDepartment,
  deleteDepartment,
  updateDepartmentStatus,
  getDepartmentByArazCity,
} = require("../controllers/department.controller");

const {
  addDepartmentSchema,
  getSingleDepartmentSchema,
  editDepartmentSchema,
  deleteDepartmentSchema,
  updateDepartmentStatusSchema,
  getDepartmentByArazCitySchema,
} = require("../validations/department.validation");

const router = require("express").Router();

router.get("/get", getAllDepartments);

router.post("/add", validate(addDepartmentSchema, "body"), addDepartment);

router.get(
  "/get/:id",
  validate(getSingleDepartmentSchema, "params"),
  getSingleDepartment
);

router.put("/edit", validate(editDepartmentSchema, "body"), editDepartment);

router.delete(
  "/delete/:id",
  validate(deleteDepartmentSchema, "params"),
  deleteDepartment
);

router.get(
  "/get/by-araz-city/:id",
  validate(getDepartmentByArazCitySchema, "params"),
  getDepartmentByArazCity
);

module.exports = router;
