const {
  api<PERSON><PERSON><PERSON>,
  apiResponse,
  apiError,
} = require("../../../../utils/api.util");
const { FETCH, CUSTOM_ERROR } = require("../../../../utils/message.util");
const { isEmpty, toObjectId } = require("../../../../utils/misc.util");
const Hotel = require("../../muqimeenMehmaanMapping/models/hotel.model");
const { WaazVenue, MawaidVenue } = require("../../../zonesCapacity/models");
const {
  KGUser,
  HierarchyPosition,
  ArazCity,
  KGType,
  SystemRole,
  Miqaat,
  ArazCityZone,
  Department,
  Interest,
  KgRequisitionByDepartment,
  CompileList,
} = require("../../../hierarchy/models");
const constants = require("../../../../constants");
const { redisCacheKeys, getCache, setCache } = require("../../../../utils/redis.cache");
const { isActiveMiqaatAndArazCity } = require("../../../../utils/checkActiveMiqaatAndArazcityStatus");

const getArazCityZoneWiseReport = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID, compileListID = [] } = req.body;
  const redisCacheKey = `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.REPORTS}:${miqaatID}:${arazCityID}:${compileListID.sort().join(':')}`;
  const data = await getCache(redisCacheKey, miqaatID, arazCityID);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "Araz City Zone Report", data, res, true);
  }

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const requisitionCount = await ArazCity.findById(arazCityID)
    .lean()
    .then((arazCity) => {
      return arazCity.departments.reduce((total, dep) => {
        return total + dep.requirement;
      }, 0);
    });

  let totalSMERecommendation = 0;
  if (compileListID && compileListID.length) {
    totalSMERecommendation = await CompileList.findById(compileListID)
      .lean()
      .then((compileList) => {
        return compileList.departments.reduce((total, dep) => {
          return total + dep.kgUsers.length;
        }, 0);
      });
  }

  const pipeline = [
    {
      $unwind: "$miqaats",
    },
    {
      $match: {
        "miqaats.arazCityID": toObjectId(arazCityID),
        "miqaats.miqaatID": toObjectId(miqaatID),
      },
    },
    {
      $group: {
        _id: null,
        totalAssigned: {
          $sum: {
            $cond: {
              if: {
                $and: [
                  { $ifNull: ["$miqaats.hierarchyPositionID", false] },
                  { $ne: ["$miqaats.hierarchyPositionID", null] },
                  { $eq: ["$miqaats.isActive", true] },
                ],
              },
              then: 1,
              else: 0,
            },
          },
        },
        totalUnassigned: {
          $sum: {
            $cond: {
              if: {
                $or: [
                  {
                    $and: [
                      { $eq: ["$miqaats.hierarchyPositionID", null] },
                      { $eq: ["$miqaats.isActive", true] },
                    ],
                  },
                  {
                    $and: [
                      {
                        $eq: [
                          { $ifNull: ["$miqaats.hierarchyPositionID", null] },
                          null,
                        ],
                      },
                      { $eq: ["$miqaats.isActive", true] },
                    ],
                  },
                  {
                    $and: [
                      { $eq: ["$miqaats.isActive", false] },
                      { $in: ["$miqaats.status", ["DECLINED", "DELETED"]] },
                    ],
                  },
                ],
              },
              then: 1,
              else: 0,
            },
          },
        },
      },
    },
    {
      $project: {
        _id: 0,
        totalAssigned: 1,
        totalUnassigned: 1,
      },
    },
  ];

  const result = await KGUser.aggregate(pipeline);
  const { totalAssigned, totalUnassigned } = result[0];

  const arazCityZones = await ArazCity.findById(arazCityID)
    .lean()
    .populate("arazCityZones")
    .then((arazCity) => {
      return arazCity.arazCityZones;
    });

  const CMZAndAllOtherZones = {};
  const arazCityZonesData = [];

  for (const arazCityZone of arazCityZones) {
    const kgRequisitionByDepartment = await KgRequisitionByDepartment.find({
      arazCityZoneID: toObjectId(arazCityZone._id),
      miqaatID: toObjectId(miqaatID),
      arazCityID: toObjectId(arazCityID),
    })
      .lean()
      .then((requisitions) => {
        return requisitions.reduce((total, r) => {
          return total + r.requiredBairaoKgCount + r.requiredMardoKgCount;
        }, 0);
      });
    arazCityZone.kgRequisitionByDepartment = kgRequisitionByDepartment || 0;

    const assignedKg = await KGUser.find({
      "miqaats.miqaatID": toObjectId(miqaatID),
      "miqaats.arazCityID": toObjectId(arazCityID),
      "miqaats.arazCityZoneID": toObjectId(arazCityZone._id),
      "miqaats.isActive": true,
    })
      .lean()
      .then((kgUsers) => kgUsers?.length || 0);

    const kgPool = await KGUser.aggregate([
      {
        $match: {
          miqaats: {
            $elemMatch: {
              miqaatID: toObjectId(miqaatID),
              arazCityID: toObjectId(arazCityID),
              isActive: true,
              $or: [
                { hierarchyPositionID: null },
                { hierarchyPositionID: { $exists: false } },
              ],
            },
          },
        },
      },
      {
        $lookup: {
          from: "interests",
          localField: "_id",
          foreignField: "userID",
          as: "interest",
        },
      },
      {
        $match: {
          "interest.arazCityZoneID": toObjectId(arazCityZone._id),
        },
      },
      {
        $group: {
          _id: null,
          kgPool: { $sum: 1 },
        },
      },
    ]).then((res) => res[0]?.kgPool || 0);

    const shortage = kgRequisitionByDepartment - assignedKg - kgPool;
    const data = {
      name: arazCityZone.name,
      assignedKg,
      kgPool,
      kgRequisitionByDepartment,
      shortage,
    };

    if (
      arazCityZone?._id?.toString() ===
      constants.ARAZ_CITY_ZONES.CMZ[0].toString()
    ) {
      if (compileListID && compileListID.length) {
        const CMZ_SmeCount = await CompileList.findById(compileListID)
          .lean()
          .populate({
            path: "departments.kgUsers",
            model: "KGUser",
            match: { "smeRecommendation.khidmatZone": "CMZ" },
          })
          .then((compileList) => {
            return compileList.departments.reduce((total, dep) => {
              return (
                total +
                dep.kgUsers.filter(
                  (kgUser) =>
                    kgUser.smeRecommendation?.khidmatZone === "CMZ"
                ).length
              );
            }, 0);
          });
        data.smeRecommendation = CMZ_SmeCount;
      }
      CMZAndAllOtherZones["CMZ"] = data;
    } else {
      if (!CMZAndAllOtherZones["OTHERZONE"]) {
        CMZAndAllOtherZones["OTHERZONE"] = {
          name: "ALL OTHER ZONES",
          assignedKg: 0,
          kgPool: 0,
          kgRequisitionByDepartment: 0,
          shortage: 0,
        };
      }
      CMZAndAllOtherZones["OTHERZONE"].assignedKg += assignedKg;
      CMZAndAllOtherZones["OTHERZONE"].kgPool += kgPool;
      CMZAndAllOtherZones["OTHERZONE"].kgRequisitionByDepartment += kgRequisitionByDepartment;
      CMZAndAllOtherZones["OTHERZONE"].shortage += shortage;
      arazCityZonesData.push(data);
    }
  }

  const assignedNotApplicableZoneKG = await KGUser.find({
    miqaats: {
      $elemMatch: {
        miqaatID: toObjectId(miqaatID),
        arazCityID: toObjectId(arazCityID),
        arazCityZoneID: null,
        isActive: true,
        hierarchyPositionID: { $exists: true }
      }
    }
  })
    .lean()
    .then((kgUsers) => kgUsers?.length || 0);

  const kgpoolNotApplicableZoneKG = await KGUser.aggregate([
    {
      $match: {
        miqaats: {
          $elemMatch: {
            miqaatID: toObjectId(miqaatID),
            arazCityID: toObjectId(arazCityID),
            $or: [
              { hierarchyPositionID: null },
              { hierarchyPositionID: { $exists: false } },
            ],
          },
        },
      },
    },
    {
      $lookup: {
        from: "interests",
        localField: "_id",
        foreignField: "userID",
        as: "interest",
      },
    },
    {
      $match: {
        $or: [
          { interest: { $size: 0 } },
          {
            $or: [
              { "interest.arazCityZoneID": null },
              { "interest.arazCityZoneID": { $exists: false } },
            ],
          },
        ],
      },
    },
    {
      $group: {
        _id: null,
        kgPool: { $sum: 1 },
      },
    },
  ]).then((res) => res[0]?.kgPool || 0);


  CMZAndAllOtherZones["ZONE_NOT_APPLICABLE"] = {
    name: "Not Available Zone",
    assignedKg: assignedNotApplicableZoneKG,
    kgPool: kgpoolNotApplicableZoneKG,
  };

  if (compileListID && compileListID.length) {
    const ZONE_SmeCount = await CompileList.findById(compileListID)
      .lean()
      .populate({
        path: "departments.kgUsers",
        model: "KGUser",
        match: { "smeRecommendation.khidmatZone": "ZONE" },
      })
      .then((compileList) => {
        return compileList.departments.reduce((total, dep) => {
          return (
            total +
            dep.kgUsers.filter(
              (kgUser) =>
                kgUser.smeRecommendation?.khidmatZone === "ZONE"
            ).length
          );
        }, 0);
      });
    CMZAndAllOtherZones.OTHERZONE.smeRecommendation = ZONE_SmeCount;

    const NOT_ZONE_APPLICABLE_SmeCount = await CompileList.findById(
      compileListID
    )
      .lean()
      .populate({ path: "departments.kgUsers", model: "KGUser" })
      .then((compileList) => {
        return compileList.departments.reduce((total, dep) => {
          return (
            total +
            dep.kgUsers.filter(
              (kgUser) => !kgUser.smeRecommendation?.khidmatZone
            ).length
          );
        }, 0);
      });

    CMZAndAllOtherZones.ZONE_NOT_APPLICABLE.
      smeRecommendation = NOT_ZONE_APPLICABLE_SmeCount

  }



  const totalArazCitySummery = {
    totalRequired: requisitionCount,
    totalAssigned,
    totalKGPool: totalUnassigned,
    totalSortage:
      requisitionCount -
      (totalSMERecommendation + totalAssigned + totalUnassigned),
  };
  if (compileListID && compileListID.length) {
    totalArazCitySummery.totalSMERecommendation = totalSMERecommendation
  }

  const finalObj = {
    arazCitySummary: totalArazCitySummery,
    CMZAndAllOtherZones,
    arazCityZonesData,
  };

  apiResponse(FETCH, "Araz City Zone Wise Report", finalObj, res);
  await setCache(redisCacheKey, finalObj, miqaatID, arazCityID);
});

module.exports = {
  getArazCityZoneWiseReport,
};
