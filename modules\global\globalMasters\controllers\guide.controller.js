const { SYSTEM_ROLES } = require("../../../../constants");
const {
  apiHandler,
  apiResponse,
  apiError,
} = require("../../../../utils/api.util");
const { ObjectId } = require("mongodb");
const {
  generatePreSignedURLs,
  uploadFileToS3,
  generateGetPreSignedURL,
} = require("../../../../utils/aws.util");
const {
  FETCH,
  ADD_SUCCESS,
  NOT_FOUND,
  DELETE_SUCCESS,
  UPDATE_SUCCESS,
  CUSTOM_SUCCESS,
  CUSTOM_ERROR,
} = require("../../../../utils/message.util");
const { isEmpty, toObjectId } = require("../../../../utils/misc.util");
const { Guide } = require("../models/guide.model");
const { KGUser } = require("../../../hierarchy/models");

const uploadFiles = apiHandler(async (req, res) => {
  if (!req.files || req.files.length === 0) {
    return apiResponse(CUSTOM_ERROR, "No files uploaded", {}, res);
  }

  const awsGroupID = req.body.awsGroupID || new ObjectId();

  const fileDetails = req.files.map((file) => ({
    fileName: file.filename,
    fileType: file.mimetype,
    fileSize: file.size,
  }));

  const preSignedURLs = await generatePreSignedURLs(
    "ashara_guide",
    awsGroupID,
    fileDetails
  );

  if (!preSignedURLs || preSignedURLs.length === 0) {
    return apiResponse(
      CUSTOM_ERROR,
      "Failed to generate pre-signed URLs",
      {},
      res
    );
  }

  const uploadPromises = preSignedURLs.map(({ fileKey, preSignedURL }, index) =>
    uploadFileToS3(req.files[index], preSignedURL, fileKey)
  );

  const uploadedFiles = (await Promise.all(uploadPromises)).filter(Boolean);

  return apiResponse(
    CUSTOM_SUCCESS,
    "Files Uploaded Successfully",
    { awsGroupID, data: uploadedFiles },
    res
  );
});

const getDownloadURL = apiHandler(async (req, res) => {
  const { fileKey } = req.body;
  const preSignedURL = await generateGetPreSignedURL(fileKey);
  return apiResponse(FETCH, "Download URL", preSignedURL, res);
});

const getAllGuides = apiHandler(async (req, res) => {
  let QueryObject = {};
  let userDepartments = [];
  
  if (
    req?.user?.systemRoleID?.toString() !==
    SYSTEM_ROLES?.SUPER_ADMIN[0]?.toString()
  ) {
    userDepartments = req?.user?.miqaats
      .filter((miqaat) => miqaat?.departmentID)
      .map((miqaat) => miqaat?.departmentID);

    if (isEmpty(userDepartments)) {
      return apiResponse(FETCH, "Guides", null, res);
    }
    QueryObject["departments"] = { $in: userDepartments };
  }

  const guides = await Guide.find(QueryObject)
    .populate("departments", "name")
    .sort({ _id: -1 });
  
  if (req?.user?.systemRoleID?.toString() !== SYSTEM_ROLES?.SUPER_ADMIN[0]?.toString()) {
    const userDeptIds = userDepartments.map(id => id.toString());
    
    guides.forEach(guide => {
      guide.departments = guide.departments.filter(department => 
        userDeptIds.includes(department._id.toString())
      );
    });
  }
  
  return apiResponse(FETCH, "Guides", guides, res);
});

const addEditGuide = apiHandler(async (req, res) => {
  const { id } = req.body;
  let data = req.body;

  if (!id) {
    data.createdBy = req.user._id;
    const newGuide = new Guide(data);
    let savedGuideData = await newGuide.save();
    return apiResponse(ADD_SUCCESS, "Guide", savedGuideData, res);
  } else {
    data.updatedBy = req.user._id;
    let updatedGuideData = await Guide.findByIdAndUpdate(id, data, {
      new: true,
    });
    if (isEmpty(updatedGuideData))
      return apiError(NOT_FOUND, "Guide", null, res);
    return apiResponse(UPDATE_SUCCESS, "Guide", updatedGuideData, res);
  }
});

const getSingleGuide = apiHandler(async (req, res) => {
  const { id } = req.params;
  const guideData = await Guide.findById(id).populate("departments", "name");

  if (isEmpty(guideData)) return apiError(NOT_FOUND, "Guide", null, res);

  return apiResponse(FETCH, "Guide", guideData, res);
});

const deleteGuide = apiHandler(async (req, res) => {
  const { id } = req.params;

  const guideData = await Guide.findOneAndDelete({ _id: id });

  if (isEmpty(guideData)) return apiError(NOT_FOUND, "Guide", null, res);

  return apiResponse(DELETE_SUCCESS, "Guide", null, res);
});

const addUserClick = apiHandler(async (req, res) => {
  const { guideID, attachmentID } = req.body;

  const attachment = await Guide.findOneAndUpdate(
    { _id: toObjectId(guideID), "attachments._id": toObjectId(attachmentID) },
    {
      $push: {
        "attachments.$.clicks": {
          userID: req.user._id,
          clickedAt: new Date(),
        },
      },
    },
    { new: true }
  );

  return apiResponse(ADD_SUCCESS, "Click", null, res);
});

const getUserClickReport = apiHandler(async (req, res) => {
  const { guideID } = req.params;

  const guide = await Guide.findById(toObjectId(guideID))
    .select("name attachments.fileName attachments.fileKey attachments.fileType attachments.clicks")
    .lean();
  if (!guide) return apiResponse(NOT_FOUND, "Guide not found", {}, res);

  const userIDs = new Set();
  for (const attachment of guide.attachments) {
    for (const click of attachment.clicks || []) {
      userIDs.add(click.userID.toString());
    }
  }

  const users = await KGUser.find(
    { _id: { $in: [...userIDs] } },
    { _id: 1, name: 1, logo: 1, ITSID: 1 }
  ).lean();

  const userMap = new Map(users.map((user) => [user._id.toString(), user]));

  for (const attachment of guide.attachments) {
    const userClickMap = new Map();
    let totalCount = 0;

    for (const click of attachment.clicks || []) {
      const userID = click.userID.toString();
      totalCount += 1;

      if (!userClickMap.has(userID)) {
        userClickMap.set(userID, {
          ...(userMap.get(userID) || {}),
          userID,
          clickCount: 0,
          lastClickedAt: click.clickedAt || null,
        });
      } else {
        if (
          click.clickedAt &&
          click.clickedAt > userClickMap.get(userID).lastClickedAt
        ) {
          userClickMap.get(userID).lastClickedAt = click.clickedAt;
        }
      }

      userClickMap.get(userID).clickCount += 1;
    }

    attachment.users = [...userClickMap.values()];
    delete attachment.clicks;

    attachment.totalCount = totalCount;
  }

  return apiResponse(FETCH, "User Click Report", guide, res);
});

module.exports = {
  getAllGuides,
  addEditGuide,
  getSingleGuide,
  deleteGuide,
  uploadFiles,
  addUserClick,
  getUserClickReport,
  getDownloadURL,
};
