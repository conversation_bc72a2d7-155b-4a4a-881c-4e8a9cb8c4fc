const {
  getAllWaazVenueSuitabilities,
  addEditWaazVenueSuitability,
  getSingleWaazVenueSuitability,
  deleteWaazVenueSuitability,
} = require("../controllers/waazVenueSuitability.controller");
const {
  addWaazVenueSuitabilitySchema,
  getSingleWaazVenueSuitabilitySchema,
  editWaazVenueSuitabilitySchema,
  deleteWaazVenueSuitabilitySchema,
} = require("../validations/waazVenueSuitability.validation");
const { validate } = require("../../../../middlewares/validation.middleware");

const router = require("express").Router();

router.get("/get", getAllWaazVenueSuitabilities);

router.post(
  "/add",
  validate(addWaazVenueSuitabilitySchema, "body"),
  addEditWaazVenueSuitability
);

router.get(
  "/get/:id",
  validate(getSingleWaazVenueSuitabilitySchema, "params"),
  getSingleWaazVenueSuitability
);

router.put(
  "/edit",
  validate(editWaazVenueSuitabilitySchema, "body"),
  addEditWaazVenueSuitability
);

router.delete(
  "/delete/:id",
  validate(deleteWaazVenueSuitabilitySchema, "params"),
  deleteWaazVenueSuitability
);

module.exports = router;
