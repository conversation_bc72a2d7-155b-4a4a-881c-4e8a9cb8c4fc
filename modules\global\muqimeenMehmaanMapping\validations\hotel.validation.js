const Joi = require("joi");
const {
  idValidation,
  stringValidation,
} = require("./../../../../utils/validator.util");

const addHotelSchema = Joi.object({
  hotelName: stringValidation.trim().required(),
  arazCityZoneID: idValidation.optional(),
  waazVenueID: Joi.string().optional().allow(""),
  arazCityID: idValidation,
  miqaatID: idValidation,
  address: Joi.string().optional().allow(""),
  latitude: Joi.string().optional().allow(""),
  longitude: Joi.string().optional().allow(""),
  googleMapLink: Joi.string().optional().allow(""),
  images: Joi.array().items(Joi.string().optional()).optional(),
});

const getHotelsSchema = Joi.object({
  arazCityZoneID: idValidation.optional(),
  waazVenueID: idValidation.optional(),
  arazCityID: idValidation.optional(),
  miqaatID: idValidation.optional(),
});

const getSingleHotelSchema = Joi.object({
  id: idValidation,
});

const updateHotelSchema = Joi.object({
  id: idValidation,
  hotelName: stringValidation.trim().required(),
  arazCityZoneID: idValidation.optional(),
  waazVenueID: Joi.string().optional().allow(""),
  arazCityID: idValidation,
  miqaatID: idValidation,
  address: Joi.string().optional().allow(""),
  latitude: Joi.string().optional().allow(""),
  longitude: Joi.string().optional().allow(""),
  googleMapLink: Joi.string().optional().allow(""),
  images: Joi.array().items(Joi.string().optional()).optional(),
});

const deleteHotelSchema = Joi.object({
  id: idValidation,
});
const getDownloadURLSchema = Joi.object({
  fileKey: stringValidation,
});

module.exports = {
  addHotelSchema,
  getHotelsSchema,
  getSingleHotelSchema,
  updateHotelSchema,
  deleteHotelSchema,
  getDownloadURLSchema,
};
