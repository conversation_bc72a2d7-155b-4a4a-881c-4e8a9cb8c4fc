const {
  api<PERSON><PERSON><PERSON>,
  apiResponse,
  apiError,
} = require("../../../../utils/api.util");
const { Document } = require("../models");
const { statusTypes } = require("../models/document.model");
const {
  FETCH,
  ADD_SUCCESS,
  UPDATE_SUCCESS,
  NOT_FOUND,
  CUSTOM_SUCCESS,
  CUSTOM_ERROR,
  DELETE_SUCCESS,
} = require("../../../../utils/message.util");
const {
  generatePreSignedURLs,
  uploadFileToS3,
  generateGetPreSignedURL,
} = require("../../../../utils/aws.util");
const { DocumentType } = require("../models/documentType.model");
const { KGUser, Department } = require("../../../hierarchy/models");
const { isEmpty } = require("../../../../utils/misc.util");
const constants = require("../../../../constants");
const { isActiveMiqaatAndArazCity } = require("../../../../utils/checkActiveMiqaatAndArazcityStatus");

const addDocument = apiHandler(async (req, res) => {
  const checkActiveStatus = await isActiveMiqaatAndArazCity(
    req.body.miqaatID,
    null,
    req
  );
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  req.body.createdBy = req.user._id;
  const document = new Document(req.body);
  await document.save();
  apiResponse(ADD_SUCCESS, "Document", document, res);
});

const getSingleDocument = apiHandler(async (req, res) => {
  const document = await Document.findById(req.params.id)
    .populate("createdBy", "name email")
    .populate("documentTypeID", "name")
    .populate("arazCityIDs", "name")
    .populate("departments", "name")
    .populate("positions", "name alias")
    .populate("systemRoleIDs", "name");
  if (!document) {
    return apiError(NOT_FOUND, "Document", null, res);
  }
  apiResponse(FETCH, "Document", document, res);
});

const editDocument = apiHandler(async (req, res) => {

  const checkActiveStatus = await isActiveMiqaatAndArazCity(
    req.body.miqaatID,
    null,
    req
  );
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Miqaat or is not active", null, res);
  }

  const document = await Document.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
  });
  if (!document) {
    return apiError(NOT_FOUND, "Document", null, res);
  }
  apiResponse(UPDATE_SUCCESS, "Document", document, res);
});

const deleteDocument = apiHandler(async (req, res) => {
  const document = await Document.findByIdAndDelete(req.params.id);
  if (!document) {
    return apiError(NOT_FOUND, "Document", null, res);
  }
  apiResponse(DELETE_SUCCESS, "Document", null, res);
});

const uploadDocument = apiHandler(async (req, res) => {
  if (!req.files || req.files.length === 0) {
    return apiResponse(CUSTOM_ERROR, "No files uploaded", {}, res);
  }

  const awsGroupID = req.user._id || new ObjectId();

  const fileDetails = req.files.map((file) => ({
    fileName: file.filename,
    fileType: file.mimetype,
    fileSize: file.size,
  }));

  const preSignedURLs = await generatePreSignedURLs(
    "document_manager",
    awsGroupID,
    fileDetails
  );

  if (!preSignedURLs || preSignedURLs.length === 0) {
    return apiResponse(
      CUSTOM_ERROR,
      "Failed to generate pre-signed URLs",
      {},
      res
    );
  }

  const uploadPromises = preSignedURLs.map(({ fileKey, preSignedURL }, index) =>
    uploadFileToS3(req.files[index], preSignedURL, fileKey)
  );

  const uploadedFiles = (await Promise.all(uploadPromises)).filter(Boolean);

  return apiResponse(
    CUSTOM_SUCCESS,
    "Files Uploaded Successfully",
    uploadedFiles,
    res
  );
});

const getDocumentURL = apiHandler(async (req, res) => {
  const { fileKey } = req.body;

  if (!fileKey) {
    return apiResponse(CUSTOM_ERROR, "File key is required", null, res);
  }

  const preSignedURL = await generateGetPreSignedURL(fileKey);

  return apiResponse(FETCH, "Document", { preSignedURL }, res);
});

const getDocumentType = apiHandler(async (req, res) => {
  const documentType = await DocumentType.find();

  if (!documentType) {
    return apiResponse(NOT_FOUND, "Document type", null, res);
  }

  return apiResponse(FETCH, "Document type", documentType, res);
});

const getDocuments = apiHandler(async (req, res) => {
  const user = await KGUser.findById(req.user._id).populate({
    path: "miqaats.hierarchyPositionID",
  });

  const { miqaatID, arazCityID } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Miqaat or Araz City is not active", null, res);
  }

  let query = {};
  if (!isEmpty(miqaatID)) {
    query.miqaatID = miqaatID;
  }

  // Filter for B type users (non-systemRoleID)
  if (!user.systemRoleID && user.treatAsCityUser && !isEmpty(arazCityID)) {
    query.$or = [
      { arazCityIDs: arazCityID },
      { arazCityIDs: { $exists: false } },
    ];
  }

  const allDocuments = await Document.find(query)
    .populate("createdBy", "name email")
    .populate("documentTypeID", "name")
    .populate("arazCityIDs", "name")
    .populate("departments", "name")
    .populate("positions", "name alias isDepartmental")
    .populate("systemRoleIDs", "name")
    .sort({ createdAt: -1 });

  // if (
  //   user.systemRoleID &&
  //   user.systemRoleID.toString() ===
  //     constants.SYSTEM_ROLES.SUPER_ADMIN[0].toString()
  // ) {
  //   return apiResponse(FETCH, "Document", allDocuments, res);
  // }
  const documents =
    user.systemRoleID && !user.treatAsCityUser
      ? await handleSystemUserAccess(user, allDocuments)
      : await handleNonSystemUserAccess(user, req, allDocuments);

  return apiResponse(FETCH, "Document", documents, res);
});

const fetchAllDocuments = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID } = req.body;
  let query = {};

  // if (!isEmpty(miqaatID)) {
  //   query.miqaatID = miqaatID;
  // }

  // if (!isEmpty(arazCityID)) {
  //   query.arazCityIDs = arazCityID;
  // }

  const documents = await Document.find(query)
    .populate("createdBy", "name email")
    .populate("documentTypeID", "name")
    .populate("arazCityIDs", "name")
    .populate("departments", "name")
    .populate("positions", "name alias")
    .populate("systemRoleIDs", "name")
    .sort({ createdAt: -1 });

  apiResponse(FETCH, "All Documents", documents, res);
});

// --- Type A User Logic ---
async function handleSystemUserAccess(user, allDocuments) {
  // if (
  //   user.systemRoleID?.toString() ===
  //   constants.SYSTEM_ROLES.SUPER_ADMIN[0].toString()
  // ) {
  //   return allDocuments;
  // }

  return allDocuments.filter((doc) => {
    const hasMatchingRole = doc.systemRoleIDs?.some(
      (role) => role._id.toString() === user.systemRoleID.toString()
    );

    if (!hasMatchingRole) return false;

    const hasNoDepartments = !doc.departments || doc.departments.length === 0;
    const hasMatchingDept = doc.departments?.some(
      (dept) => dept._id.toString() === user.systemDepartmentID?.toString()
    );

    return hasNoDepartments || hasMatchingDept;
  });
}

async function handleNonSystemUserAccess(user, req, allDocuments) {
  // All active miqaats of the user (don't filter by city)
  const activeMiqaats = user.miqaats.filter((m) => m.isActive);

  if (!activeMiqaats.length) return [];

  return allDocuments.filter((doc) => {
    // Match the document against any one of the user's active miqaats
    return activeMiqaats.some((miqaat) => {
      if (isEmpty(doc.miqaatID) || isEmpty(doc.arazCityIDs)) return false;
      if (doc.miqaatID.toString() !== miqaat.miqaatID.toString()) return false;

      // 1. ArazCity match (only if document has restrictions)
      if (doc.arazCityIDs?.length > 0) {
        const match = doc.arazCityIDs.some(
          (docCity) => docCity._id.toString() === miqaat.arazCityID.toString()
        );
        if (!match) return false;
      }

      // 2. Position match
      if (doc.positions?.length > 0) {
        const match = doc.positions.some(
          (pos) =>
            pos._id.toString() === miqaat.hierarchyPositionID?._id?.toString()
        );
        if (!match) return false;
      }

      // 3. Department match (if position is departmental)
      if (
        doc.departments?.length > 0 &&
        miqaat.hierarchyPositionID?.isDepartmental
      ) {
        const match = doc.departments.some(
          (dept) => dept._id.toString() === miqaat.departmentID?.toString()
        );
        if (!match) return false;
      }

      return true;
    });
  });
}

const getAllDocuments = apiHandler(async (req, res) => {
  let query = {};
  if (
    req.user.systemRoleID?.toString() !==
    constants.SYSTEM_ROLES.SUPER_ADMIN[0].toString()
  ) {
    query = {
      createdBy: req.user._id,
    };
  }
  if (req.body.miqaatID) {
    query.miqaatID = req.body.miqaatID;
  }
  const documents = await Document.find(query)
    .populate("createdBy", "name email")
    .populate("documentTypeID", "name")
    .populate("arazCityIDs", "name")
    .populate("departments", "name")
    .populate("positions", "name alias")
    .populate("systemRoleIDs", "name");

  apiResponse(FETCH, "All Documents", documents, res);
});

module.exports = {
  addDocument,
  getDocuments,
  getSingleDocument,
  editDocument,
  deleteDocument,
  uploadDocument,
  getDocumentURL,
  getDocumentType,
  getAllDocuments,
  fetchAllDocuments,
};
