const express = require("express");
const cors = require("cors");
const path = require("path");
const compression = require("compression");
const { CORS_ORIGIN, NODE_ENV, PORT } = require("./constants");
const routes = require("./modules");
const webhookRoutes = require("./routes/webhook.route");
const publicRoutes = require("./public/public.route");
const { authGuard, roleGuard, validateApi<PERSON>ey } = require("./middlewares/guard.middleware");
const app = express();

app.use(cors({ credentials: true, origin: CORS_ORIGIN }));
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "1mb" }));
app.use(express.static("public"));
app.use((err, req, res, next) => {
  if (err instanceof SyntaxError) {
    return res
      .status(400)
      .json({ success: false, message: "Invalid JSON format" });
  }
  next(err);
});
app.use("/assets", express.static(__dirname + "/assets"));
app.use(
  compression({
    level: 6,
    filter: (req, res) => {
      if (req.headers["x-no-compression"]) {
        return false;
      }
      return compression.filter(req, res);
    },
  })
);

const { updateConsentStatusCron } = require("./utils/cron.util");
updateConsentStatusCron();



const swaggerUi = require('swagger-ui-express');
const { swaggerDocument } = require('./utils/swagger/swagger');
const { clearAllCache } = require("./utils/redis.cache");

app.use('/api/v1/docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument, {
  explorer: true,
  customCss: '.swagger-ui .topbar { display: none }',
  swaggerOptions: {
    persistAuthorization: true,
  }
}));

app.use("/api/webhook", webhookRoutes);
// app.use("/api/v1", validateApiKey, publicRoutes);
app.use("/api", authGuard, roleGuard, routes);

if (NODE_ENV === "production") {
  app.use(express.static(path.join(__dirname, "client", "dist")));
  app.get("/", (req, res) => {
    res.sendFile(path.resolve(__dirname, "client", "dist", "index.html"));
  });
  app.get("/*", (req, res) => {
    res.sendFile(path.resolve(__dirname, "client", "dist", "index.html"));
  });
} else {
  app.get("/", (req, res) => {
    res.send(`ams is running on port: ${PORT}`);
  });
}
const clearAllCacheFromRedis = async () => {
  console.log("clearing cache");
  await clearAllCache();
}
clearAllCacheFromRedis();

module.exports = app;
