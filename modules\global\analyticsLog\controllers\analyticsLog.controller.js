const {
  apiHand<PERSON>,
  apiResponse,
  apiError,
} = require("../../../../utils/api.util");
const { AnalyticsLog } = require("../models/analyticsLog.model");
const {
  FETCH,
  ADD_SUCCESS,
  UPDATE_SUCCESS,
  NOT_FOUND,
  CUSTOM_SUCCESS,
  CUSTOM_ERROR,
  DELETE_SUCCESS,
} = require("../../../../utils/message.util");
const { KGUser, Department } = require("../../../hierarchy/models");
const { isEmpty, toObjectId } = require("../../../../utils/misc.util");
const constants = require("../../../../constants");
const dayjs = require("dayjs");

const addAnalytics = apiHandler(async (req, res) => {
  const activityData = req.body;
  const userId = req.user?._id;

  const user = await KGUser.findById(userId);
  if (!user) {
    return apiError(NOT_FOUND, "KG User", null, res);
  }
  activityData.userId = userId;
  let analytics = await AnalyticsLog.create(activityData);
  return apiResponse(
    ADD_SUCCESS,
    "Analytics",
    analytics,
    res
  );
});

const getUserAnalyticsLog = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID } = req.body;
  let days = 7;

  const reportCounts = await AnalyticsLog.aggregate([
    {
      $match: {
        miqaatID: toObjectId(miqaatID),
        arazCityID: toObjectId(arazCityID),
        action: "download_report"
      }
    },
    {
      $group: {
        _id: {
          page: "$page",
          module: "$module",
          url: "$url",
        },
        count: { $sum: 1 }
      }
    },
    {
      $project: {
        _id: 0,
        page: "$_id.page",
        module: "$_id.module",
        url: "$_id.url",
        count: 1
      }
    }
  ]);


  const topTenPageVisit = await AnalyticsLog.aggregate([
    {
      $match: {
        miqaatID: toObjectId(miqaatID),
        arazCityID: toObjectId(arazCityID),
        action: "page_visit"
      }
    },
    {
      $project: {
        _id: 1,
        module: 1,
        page: 1,
        url: 1,
        createdAt: 1
      }
    },
    {
      $group: {
        _id: {
          page: "$page",
          module: "$module",
          url: "$url"
        },
        count: { $sum: 1 },
        createdAt: { $first: "$createdAt" }
      }
    },
    {
      $sort: {
        createdAt: -1
      }
    },
    {
      $project: {
        _id: 0,
        page: "$_id.page",
        module: "$_id.module",
        url: "$_id.url",
        count: 1,
        createdAt: 1
      }
    },
    {
      $limit: 10
    }
  ]);

  // KG Added
  const today = dayjs().endOf('day').toDate();
  const startDate = dayjs().subtract(7 - 1, 'day').startOf('day').toDate();

  const kgAddedPerDay = await AnalyticsLog.aggregate([
    {
      $match: {
        action: "user_login",
        createdAt: { $gte: startDate, $lte: today },
        arazCityID: toObjectId(arazCityID),
        miqaatID: toObjectId(miqaatID),
      }
    },
    {
      $group: {
        _id: { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
        // page: { $first: "$page" },
        // module: { $first: "$module" },
        // url: { $first: "$url" },
        count: { $sum: 1 }
      }
    },
    {
      $project: {
        _id: 0,
        date: "$_id",
        count: 1
      }
    }
  ]);
  const finalKgAddedUser = prepareDataLast7Days(kgAddedPerDay, days);

  const deviceTypePerDay = await AnalyticsLog.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate, $lte: today },
        arazCityID: toObjectId(arazCityID),
        miqaatID: toObjectId(miqaatID),
      }
    },
    {
      $group: {
        _id: {
          date: { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
          deviceType: "$deviceType",
        },
        count: { $sum: 1 }
      }
    },
    {
      $project: {
        _id: 0,
        date: "$_id.date",
        deviceType: "$_id.deviceType",
        count: 1
      }
    },
    {
      $sort: { date: 1 }
    }
  ]);
  const overallInteraction = prepareInteractionChartData(deviceTypePerDay, days);

  const uniqueLogin = await AnalyticsLog.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate, $lte: today },
        arazCityID: toObjectId(arazCityID),
        miqaatID: toObjectId(miqaatID),
      }
    },
    {
      $group: {
        _id: {
          date: { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
          userId: "$userId",
        },
      }
    },
    {
      $group: {
        _id: "$_id.date",
        count: { $sum: 1 }
      }
    },
    {
      $project: {
        _id: 0,
        date: "$_id",
        count: 1
      }
    },
    {
      $sort: { date: 1 }
    }
  ]);
  const uniqueLoginOnAms = prepareDataLast7Days(uniqueLogin);

  let finalObj = {
    overallInteraction,
    uniqueLoginOnAms,
    kgAddedLast7Days: finalKgAddedUser,
    downloadReports: reportCounts,
    top10PageVisit: topTenPageVisit,
  }
  return apiResponse(FETCH, "User Analytics", finalObj, res);

  // let formattedReportCounts = reportCounts.map((r) => ({
  //   // name : r.name.split("_").map((v) => v.charAt(0).toUpperCase() + v.slice(1)).join(' '),
  //   // count: r.count
  // }));
});

function prepareDataLast7Days(data, days = 7) {
  const today = dayjs().endOf('day').toDate();
  const startDate = dayjs().subtract(days - 1, 'day').startOf('day').toDate();
  const result = [];

  for (let i = 0; i < days; i++) {
    const date = dayjs(startDate).add(i, 'day');
    const formattedDate = date.format('YYYY-MM-DD');
    const dataForDate = data.find(entry => entry.date === formattedDate);

    result.push({
      date: formattedDate,
      day: date.format('dddd'),
      count: dataForDate ? dataForDate.count : 0,
    });
  }

  return result;
}

function prepareInteractionChartData(deviceTypePerDay, days = 7) {
  const today = dayjs().endOf('day').toDate();
  const startDate = dayjs().subtract(days - 1, 'day').startOf('day').toDate();
  const result = [];

  for (let i = 0; i < days; i++) {
    const date = dayjs(startDate).add(i, 'day');
    const formattedDate = date.format('YYYY-MM-DD');
    const day = date.format('dddd');

    const appData = deviceTypePerDay.find((entry) => entry.date === formattedDate && entry.deviceType === "app");
    const webData = deviceTypePerDay.find((entry) => entry.date === formattedDate && entry.deviceType === "web");

    result.push({
      date: formattedDate,
      day,
      app: appData ? appData.count : 0,
      web: webData ? webData.count : 0
    });
  }
  return result;
}

module.exports = {
  addAnalytics,
  getUserAnalyticsLog
};
