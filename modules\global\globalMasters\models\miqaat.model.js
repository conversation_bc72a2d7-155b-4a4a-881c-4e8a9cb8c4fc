const { Schema, model } = require("mongoose");

const miqaatSchema = new Schema(
  {
    ITSID: {
      type: String,
      required: false,
      trim: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    LDName: {
      type: String,
      required: false,
      trim: true,
    },
    startDate: {
      type: Date,
      required: false,
    },
    endDate: {
      type: Date,
      required: false,
    },
    type: {
      type: String,
      required: false,
      trim: true,
    },
    status: {
      type: String,
      required: true,
      trim: true,
      enum: ["active", "inactive"],
    },
    logo: {
      type: String,
      required: false,
      trim: true,
    },
    description: {
      type: String,
      required: false,
      trim: true,
    },
    arazCities: [
      {
        type: Schema.Types.ObjectId,
        ref: "ArazCity",
        required: false,
      },
    ],
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

const Miqaat = model("Miqaat", miqaatSchema);

module.exports = { Miqaat };
