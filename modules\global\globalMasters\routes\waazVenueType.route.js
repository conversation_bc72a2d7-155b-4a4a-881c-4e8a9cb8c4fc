const {
  getAllWaazVenueTypes,
  addEditWaazVenueType,
  getSingleWaazVenueType,
  deleteWaazVenueType,
} = require("../controllers/waazVenueType.controller");
const {
  addWaazVenueTypeSchema,
  getSingleWaazVenueTypeSchema,
  editWaazVenueTypeSchema,
  deleteWaazVenueTypeSchema,
} = require("../validations/waazVenueType.validation");
const { validate } = require("../../../../middlewares/validation.middleware");

const router = require("express").Router();

router.get("/get", getAllWaazVenueTypes);

router.post(
  "/add",
  validate(addWaazVenueTypeSchema, "body"),
  addEditWaazVenueType
);

router.get(
  "/get/:id",
  validate(getSingleWaazVenueTypeSchema, "params"),
  getSingleWaazVenueType
);

router.put(
  "/edit",
  validate(editWaazVenueTypeSchema, "body"),
  addEditWaazVenueType
);

router.delete(
  "/delete/:id",
  validate(deleteWaazVenueTypeSchema, "params"),
  deleteWaazVenueType
);

module.exports = router;
