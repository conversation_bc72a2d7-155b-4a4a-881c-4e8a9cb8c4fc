const router = require("express").Router();
const { validate } = require("../../../../middlewares/validation.middleware");
const { getSmeMappingUsers, addSmeMappingUser, deleteSmeUser } = require("../controllers/smeMapping.controller");
const { addSmeMappingUserSchema, deleteSmeUserSchema } = require("../validations/smeMapping.validation");

router.get("/get", getSmeMappingUsers);

router.post("/add", validate(addSmeMappingUserSchema, "body"), addSmeMappingUser)

router.patch("/delete", validate(deleteSmeUserSchema, "body"), deleteSmeUser)

module.exports = router;