const {
  getAllWaazVenuePriorities,
  addEditWaazVenuePriority,
  getSingleWaazVenuePriority,
  deleteWaazVenuePriority,
} = require("../controllers/waazVenuePriority.controller");
const {
  addWaazVenuePrioritySchema,
  getSingleWaazVenuePrioritySchema,
  editWaazVenuePrioritySchema,
  deleteWaazVenuePrioritySchema,
} = require("../validations/waazVenuePriority.validation");
const { validate } = require("../../../../middlewares/validation.middleware");

const router = require("express").Router();

router.get("/get", getAllWaazVenuePriorities);

router.post(
  "/add",
  validate(addWaazVenuePrioritySchema, "body"),
  addEditWaazVenuePriority
);

router.get(
  "/get/:id",
  validate(getSingleWaazVenuePrioritySchema, "params"),
  getSingleWaazVenuePriority
);

router.put(
  "/edit",
  validate(editWaazVenuePrioritySchema, "body"),
  addEditWaazVenuePriority
);

router.delete(
  "/delete/:id",
  validate(deleteWaazVenuePrioritySchema, "params"),
  deleteWaazVenuePriority
);

module.exports = router;
