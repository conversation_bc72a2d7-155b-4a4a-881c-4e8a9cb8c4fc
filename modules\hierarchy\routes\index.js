const express = require("express");
const router = express.Router();

const arazCityZoneRoute = require("./arazCityZone.route");
const systemUserRoute = require("./systemUser.route");
const arazCityDepartmentRoute = require("./arazCityDepartment.route");

const jamiatJamaatRoute = require("./jamiatJamaat.route");
const hierarchyRoute = require("./hierarchy.route");
const kgUserRoute = require("./kgUser.route");
const systemRoleRoute = require("./systemRole.route");
const healthcheckRoute = require("./healthcheck.route");
const dashboardRoute = require("./dashboard.route");
const departmentReportRoute = require("./departmentReport.route");
const arazCityZoneReportRoute = require("./arazCityZoneReport.route");
const arazCityReportRoute = require("./arazCityReport.route");

const arazCityZoneFilesRoute = require("./arazCityZoneFile.route");

const recommendUserRoute = require("./recommendUser.route");
const compileUserListRoute = require("./compileUserList.route");
const razaListRoute = require("./razaList.route");
const interestRoute = require("./interest.route");
const zoneTeamReportRoute = require("./zoneTeamReport.route");
const kgRequisitionByDepartmentRoute = require("./kgRequisitionByDepartment.route");
const kgRequisitionByZoneRoute = require("./kgRequisitionByZone.route");
const approvedCompileListRoute = require("./approvedCompileList.route");
const print = require("./print.route")
const unprint = require("./unprint.route")

router.use("/unprint", unprint);
router.use("/print", print);
router.use("/healthcheck", healthcheckRoute);
router.use("/hierarchy", hierarchyRoute);
router.use("/dashboard", dashboardRoute);
router.use("/jamiat-jamaat", jamiatJamaatRoute);
router.use("/system-user", systemUserRoute);
router.use("/araz-city-department", arazCityDepartmentRoute);
router.use("/araz-city-zone", arazCityZoneRoute);
router.use("/kg-user", kgUserRoute);
router.use("/system-role", systemRoleRoute);
router.use("/department-report", departmentReportRoute);
router.use("/araz-city-report", arazCityReportRoute);
router.use("/araz-city-zone-report", arazCityZoneReportRoute);
router.use("/interest", interestRoute);
router.use("/araz-city-zone-file", arazCityZoneFilesRoute);
router.use("/araz-city-zone-team-report", zoneTeamReportRoute);
router.use("/kg-requisition-by-department", kgRequisitionByDepartmentRoute);
router.use("/kg-requisition-by-zone", kgRequisitionByZoneRoute);
router.use("/recommend-users", recommendUserRoute);
router.use("/compile-user-list", compileUserListRoute);
router.use("/approved-compile-lists",approvedCompileListRoute)
router.use("/raza-list", razaListRoute)



//to be remove in future
const arazCityRoute = require("../../global/globalMasters/routes/arazCity.route");
const departmentRoute = require("../../global/globalMasters/routes/department.route");
const miqaatRoute = require("../../global/globalMasters/routes/miqaat.route");
const kgTypeRoute = require("../../global/globalMasters/routes/kgType.route");
const kgGroupRoute = require("../../global/globalMasters/routes/kgGroup.route");
const functionRoute = require("../../global/globalMasters/routes/function.route");
const hierarchyPositionRoute = require("../../global/globalMasters/routes/hierarchyPosition.route");
const smeMappingRoute = require("../../global/globalMasters/routes/smeMapping.route");
router.use("/miqaat", miqaatRoute);
router.use("/department", departmentRoute);
router.use("/araz-city", arazCityRoute);
router.use("/kg-type", kgTypeRoute);
router.use("/kg-group", kgGroupRoute);
router.use("/function", functionRoute);
router.use("/kg-hierarchy-position", hierarchyPositionRoute);
router.use("/sme-mapping", smeMappingRoute);


const kgRequisitionRoute = require("./kgRequistion.route");
const kgRequisitionApplicationRoute = require("./kgRequistionApplication.route");

router.use("/kg-requisition", kgRequisitionRoute);
router.use("/kg-requisition-application", kgRequisitionApplicationRoute);


module.exports = router;
