const { generateEntries } = require("./utils/schema.util");

require("dotenv").config({
  path: "./.env",
});

const SUPER_ADMIN = "6789170d3f8ce68a7c508a91";
const PMO = "6789170d3f8ce68a7c508a93";
const AAMIL_SAHEB = "6789170d3f8ce68a7c508a94";
const IPMO = "6789170d3f8ce68a7c508a97";
const CENTRAL_HR = "67f6238a4b80130930145fc5";
const PMO_DEFAULT = "680b1f3088445fb7c9ebcc47";


module.exports = {
  // General
  NODE_ENV: process.env.NODE_ENV,
  CORS_ORIGIN: process.env.CORS_ORIGIN,
  PORT: process.env.PORT,
  BASE_URL: process.env.BASE_URL_BACKEND,
  IMAGE_BASE_URL: process.env.IMAGE_BASE_URL,

  // Microservice Ports
  GATEWAY_PORT: process.env.GATEWAY_PORT,
  HIERARCHY_PORT: process.env.HIERARCHY_PORT,
  COMMUNICATION_PORT: process.env.COMMUNICATION_PORT,
  ZONES_CAPACITY_PORT: process.env.ZONES_CAPACITY_PORT,
  TASK_MANAGEMENT_PORT: process.env.TASK_MANAGEMENT_PORT,
  SURVEY_PORT: process.env.SURVEY_PORT,
  AUTH_SERVICE_PORT: process.env.AUTH_SERVICE_PORT,
  GLOBAL_PORT: process.env.GLOBAL_PORT,

  //Email
  SMTP_SERVER:process.env.SMTP_SERVER,
  SMTP_PORT:process.env.SMTP_PORT,
  SMTP_USER:process.env.SMTP_USER,
  SMTP_PASSWORD:process.env.SMTP_PASSWORD,
  SMTP_FROM_EMAIL:process.env.SMTP_FROM_EMAIL,
  SMTP_FROM_NAME:process.env.SMTP_FROM_NAME,

  //Links
  IOS_APP_LINK: process.env.IOS_APP_LINK,
  ANDROID_APP_LINK: process.env.ANDROID_APP_LINK,

  // Auth
  ITS_AUTH_TOKEN: process.env.ITS_AUTH_TOKEN,
  ITS_USER_HCODE: process.env.ITS_USER_HCODE,
  ITS_USER_PHOTO_HCODE: process.env.ITS_USER_PHOTO_HCODE,
  ITS_USER_QA_PHOTO_HCODE: process.env.ITS_USER_QA_PHOTO_HCODE,
  ITS_TOKEN_KEY: process.env.ITS_TOKEN_KEY,
  ITS_JAMAAT_WISE_HOF_HCODE: process.env.ITS_JAMAAT_WISE_HOF_HCODE,
  ITS_JAMIAT_WISE_HOF_HCODE: process.env.ITS_JAMIAT_WISE_HOF_HCODE,
  ITS_HOF_WISE_MEMBER_STATS_HCODE: process.env.ITS_HOF_WISE_MEMBER_STATS_HCODE,
  ITS_JAMIAT_JAMAAT_MASTER_RECORD_HCODE: process.env.ITS_JAMIAT_JAMAAT_MASTER_RECORD_HCODE,


  // DB
  DB_NAME: process.env.DB_NAME,
  MONGODB_URI: process.env.MONGODB_URI,

  //S3
  AWS_BUCKET_NAME: process.env.AWS_BUCKET_NAME,
  AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,
  AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,
  AWS_REGION: process.env.AWS_REGION,

  //CLOUDFRONT
  CLOUDFRONT_URL: process.env.CLOUDFRONT_URL,

  // Secrets
  TOKEN_SECRET: process.env.TOKEN_SECRET,
  JWT_SECRET: process.env.JWT_SECRET,
  CRYPTO_SECRET: process.env.CRYPTO_SECRET,

  // One Signal
  ONE_SIGNAL_APP_ID: process.env.ONE_SIGNAL_APP_ID,
  ONE_SIGNAL_API_KEY_ID: process.env.ONE_SIGNAL_API_KEY_ID,

  // AWS
  AWS_REGION: process.env.AWS_REGION,
  AWS_BUCKET_NAME: process.env.AWS_BUCKET_NAME,
  AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,
  AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,

  // REDIS
  REDIS_HOST: process.env.REDIS_HOST,
  REDIS_PORT: process.env.REDIS_PORT,
  REDIS_PASSWORD: process.env.REDIS_PASSWORD,

  // OPEN PROJECT
  OPEN_PROJECT_BASE_URL: process.env.OPEN_PROJECT_BASE_URL,
  OPEN_PROJECT_API_KEY: process.env.OPEN_PROJECT_API_KEY,

  //MIQAAT HR API KEY
  MIQAAT_HR_API_KEY:process.env.MIQAAT_HR_API_KEY,
  MIQAAT_HR_MIQAAT_ID:process.env.MIQAAT_HR_MIQAAT_ID,
  MIQAAT_HR_URL:process.env.MIQAAT_HR_URL,

  // Default Entries
  KG_GROUPS: generateEntries("KG_GROUPS", {
    LEADERSHIP: ["678915d672fa3ee5bb648dbf", "leadership"],
    EXECUTIVE_COMMITTEE: ["678915d672fa3ee5bb648dc0", "executive_committe"],
    ZONAL: ["678915d672fa3ee5bb648dc1", "zonal"],
    PROJECT_COORDINATOR: ["678fbaba8c203b0ca7d3ad6d", "project_coordinator"],
  }),
  KG_TYPES: generateEntries("KG_TYPES", {
    LOCAL_KG: ["678915d672fa3ee5bb648dc2", "local_kg"],
    INTERNATIONAL_PLUGIN: ["678915d672fa3ee5bb648dc3", "international_plug-in"],
    INTERNATIONAL_MEMBER: ["678915d672fa3ee5bb648dc4", "international_member"],
    HISB_E_SAIFY: ["678915d672fa3ee5bb648dc5", "hisb-e-saify"],
    HAADI_ASHAR: ["678915d672fa3ee5bb648dc6", "haadi_ashar"],
    PROJECT_COORDINATOR: [
      "678915d672fa3ee5bb648dc7",
      "project_coordinator_(pmo)",
    ],
    QASRE_AALI: ["678915d672fa3ee5bb648dc9", "qasre_aali"],
    DAIRATUL_AQEEQ: ["679b29d2c6b35bcef2928d1f", "dairatul_aqeeq"],
    MUSHRIF: ["67f769c7672160fde3b01ed9", "mushrif"],
  }),
  SYSTEM_ROLES: generateEntries("SYSTEM_ROLES", {
    SUPER_ADMIN: ["6789170d3f8ce68a7c508a91", "super_admin"],
    ARAZ_CITY_ADMIN: ["6789170d3f8ce68a7c508a92", "araz_city_admin"],
    PMO: ["6789170d3f8ce68a7c508a93", "pmo"],
    AAMIL_SAHEB: ["6789170d3f8ce68a7c508a94", "aamil_saheb"],
    JAMAAT_BOARD: ["6789170d3f8ce68a7c508a95", "jamaat_board"],
    ZONAL_CHAIRMAN: ["6789170d3f8ce68a7c508a96", "zonal_chairman"],
    IPMO: ["6789170d3f8ce68a7c508a97", "ipmo"],
    DEFAULT: ["6791e23e23d7780affd68fe7", "default"],
    HR_DEFAULT: ["6791e23e23d7780affd68fe8", "hr_default"],
    FINANCE_DEFAULT: ["6791e23e23d7780affd68fe9", "finance_default"],
    PROCUREMENT_DEFAULT: ["6791e23e23d7780affd68fea", "procurement_default"],
    PR_GOVT_RELATIONS_DEFAULT: [
      "6791e23e23d7780affd68feb",
      "pr_govt_relations_default",
    ],
    ZONAL_DEFAULT: ["6791e23e23d7780affd68fec", "zonal_default"],
    CENTRAL_MASJID_DEFAULT: [
      "6791e23e23d7780affd68fed",
      "central_masjid_default",
    ],
    AVIT_DEFAULT: ["6791e23e23d7780affd68fee", "avit_default"],
    MAWAID_DEFAULT: ["6791e23e23d7780affd68fef", "mawaid_default"],
    TRANSPORT_DEFAULT: ["6791e23e23d7780affd68ff0", "transport_default"],
    FLOW_MANAGEMENT_DEFAULT: [
      "6791e23e23d7780affd68ff1",
      "flow_management_default",
    ],
    NAZAFAT_DEFAULT: ["6791e23e23d7780affd68ff2", "nazafat_default"],
    MUMINEEN_MEHMAAN_RECEPTION_DEFAULT: [
      "6791e23e23d7780affd68ff3",
      "mumineen_mehmaan_reception_default",
    ],
    IT_TECHNOLOGY_SERVICES_AND_SUPPORT_DEFAULT: [
      "6791e23e23d7780affd68ff4",
      "it_technology_services_and_support_default",
    ],
    CENTRAL_OFFICE_DEFAULT: [
      "6791e23e23d7780affd68ff5",
      "central_office_default",
    ],
    FIRE_SAFETY_AND_OVERALL_HSE_DEFAULT: [
      "6791e23e23d7780affd68ff6",
      "fire_safety_and_overall_hse_default",
    ],
    ACCOMODATION_DEFAULT: ["6791e23e23d7780affd68ff7", "accomodation_default"],
    SECURITY_DEFAULT: ["6791e23e23d7780affd68ff8", "security_default"],
    WELL_BEING_DEFAULT: ["6791e23e23d7780affd68ff9", "well_being_default"],
    FOOD_HYGEINE_AND_CONTAMINATION_DEFAULT: [
      "6791e23e23d7780affd68ffa",
      "food_hygeine_and_contamination_default",
    ],
    MEDICAL_DEFAULT: ["6791e23e23d7780affd68ffb", "medical_default"],
    COMMUNICATIONS_DEFAULT: [
      "6791e23e23d7780affd68ffc",
      "communications_default",
    ],
    OHBAT_WAAZ_TALLAQI_DEFAULT: [
      "6791e23e23d7780affd68ffd",
      "ohbat_waaz_tallaqi_default",
    ],
    ITS_DEFAULT: ["6791e23e23d7780affd68ffe", "its_default"],
    NOT_ASSIGNED: ["67f36bf64dbd28b17d7f77ee", "not_assigned"],
    SME_USER: ["68037da48d95e20d03c81a3e", "sme_user"],
    RECOMMENDED_USER: ["68037dbd8d95e20d03c81a3f", "recommended_user"],
  }),
  ARAZ_CITY_ZONES: generateEntries("ARAZ_CITY_ZONES", {
    CMZ: ["678cc22ca4f5fc5d18b3cb8a", "cmz"],
  }),
  FUNCTIONS: generateEntries("FUNCTIONS", {
    OTHER_FUNCTION: ["6815adca3f8aa2c622c8f582", "other_function"],
  }),
  HIERARCHY_POSITIONS: generateEntries("HIERARCHY_POSITIONS", {
    IPMO: ["678ca7ce4a83be4c2339027c", "ipmo", "top-most", false, false, []],
    AAMIL_SAHEB: [
      "678ca82d4a83be4c23390284",
      "aamil_saheb",
      "single",
      false,
      false,
      ["IPMO"],
    ],
    // STRATEGIC_PLANNER: [
    //   "678ca82d4a83be4c23390285",
    //   "strategic_planner",
    //   "single",
    //   false,
    //   false,
    //   ["AAMIL_SAHEB"],
    // ],
    JAMAAT_BOARD: [
      "678ca8684a83be4c23390298",
      "jamaat_board",
      "single",
      false,
      false,
      ["STRATEGIC_PLANNER"],
    ],
    LEAD_AQA_MOULA_UTARO: [
      "678ca8804a83be4c233902a2",
      "lead_aqa_maula_utaro",
      "single",
      false,
      false,
      ["STRATEGIC_PLANNER"],
    ],
    PMO: [
      "678ca84a4a83be4c2339028e",
      "pmo",
      "single",
      false,
      false,
      ["STRATEGIC_PLANNER"],
    ],
    ZONAL_CHAIRMAN: [
      "678ca9454a83be4c233902c0",
      "zonal_chairman",
      "top-most",
      false,
      false,
      ["PMO"],
    ],
    HOD: ["678ca8d64a83be4c233902ac", "hod", "top-most", false, true, ["PMO"]],
    ZONE_HEAD: [
      "678ca95d4a83be4c233902ca",
      "zone_head",
      "top-most",
      true,
      false,
      ["ZONAL_CHAIRMAN"],
    ],
    ZONE_LEAD: [
      "678cafae4a83be4c2339039d",
      "zone_lead",
      "double",
      true,
      true,
      ["ZONE_HEAD", "HOD"],
    ],
    ZONE_TEAM: [
      "678cb1454a83be4c233903b6",
      "zone_team",
      "single",
      true,
      true,
      ["ZONE_LEAD"],
    ],
    HOD_TEAM:[
      "681b0354a892ac1747e332a2",
      "hod_team",
      "single",
      false,
      true,
      ["HOD"],
    ]
  }),
  DEPARTMENTS: generateEntries("DEPARTMENTS", {
    HR: [
      "678f727adad6b45d5c4e36e8",
      "hr",
      true,
      false,
      ["HOD"],
      "6791e23e23d7780affd68fe8",
    ],
    FINANCE: [
      "678919fb26d4672f519ac625",
      "finance",
      true,
      false,
      ["HOD"],
      "6791e23e23d7780affd68fe9",
    ],
    PROCUREMENT: [
      "678919fb26d4672f519ac626",
      "procurement",
      true,
      false,
      ["HOD"],
      "6791e23e23d7780affd68fea",
    ],
    PR_GOVT_RELATIONS: [
      "678919fb26d4672f519ac627",
      "pr_/_govt_relations",
      true,
      false,
      ["HOD"],
      "6791e23e23d7780affd68feb",
    ],
    ZONAL: [
      "678919fb26d4672f519ac628",
      "zonal",
      true,
      false,
      ["HOD"],
      "6791e23e23d7780affd68fec",
    ],
    CENTRAL_MASJID: [
      "678919fb26d4672f519ac629",
      "central_masjid",
      true,
      false,
      ["HOD"],
      "6791e23e23d7780affd68fed",
    ],
    AVIT: [
      "678919fb26d4672f519ac62a",
      "avit",
      true,
      false,
      ["HOD"],
      "6791e23e23d7780affd68fee",
    ],
    MAWAID: [
      "678919fb26d4672f519ac62b",
      "mawaid",
      true,
      false,
      ["HOD"],
      "6791e23e23d7780affd68fef",
    ],
    TRANSPORT: [
      "678919fb26d4672f519ac62c",
      "transport",
      true,
      false,
      ["HOD"],
      "6791e23e23d7780affd68ff0",
    ],
    FLOW_MANAGEMENT: [
      "678919fb26d4672f519ac62d",
      "flow_management",
      true,
      false,
      ["HOD"],
      "6791e23e23d7780affd68ff1",
    ],
    NAZAFAT: [
      "678919fb26d4672f519ac62e",
      "nazafat",
      true,
      false,
      ["HOD"],
      "6791e23e23d7780affd68ff2",
    ],
    MUMINEEN_MEHMAAN_RECEPTION: [
      "678919fb26d4672f519ac62f",
      "mumineen_mehmaan_reception",
      true,
      false,
      ["HOD"],
      "6791e23e23d7780affd68ff3",
    ],
    IT_TECHNOLOGY_SERVICES_AND_SUPPORT: [
      "678919fb26d4672f519ac630",
      "it_/_technology_services_and_support",
      true,
      false,
      ["HOD"],
      "6791e23e23d7780affd68ff4",
    ],
    CENTRAL_OFFICE: [
      "678919fb26d4672f519ac631",
      "central_office",
      true,
      false,
      ["HOD"],
      "6791e23e23d7780affd68ff5",
    ],
    FIRE_SAFETY_AND_OVERALL_HSE: [
      "678919fb26d4672f519ac632",
      "fire_safety_and_overall_hse",
      true,
      false,
      ["HOD"],
      "6791e23e23d7780affd68ff6",
    ],
    ACCOMODATION: [
      "678919fb26d4672f519ac633",
      "accomodation",
      true,
      false,
      ["HOD"],
      "6791e23e23d7780affd68ff7",
    ],
    SECURITY: [
      "678919fb26d4672f519ac634",
      "security",
      true,
      false,
      ["HOD"],
      "6791e23e23d7780affd68ff8",
    ],
    WELL_BEING: [
      "678919fb26d4672f519ac635",
      "well_being",
      true,
      false,
      ["HOD"],
      "6791e23e23d7780affd68ff9",
    ],
    FOOD_HYGEINE_AND_CONTAMINATION: [
      "678919fb26d4672f519ac636",
      "food_hygeine_and_contamination",
      true,
      false,
      ["HOD"],
      "6791e23e23d7780affd68ffa",
    ],
    MEDICAL: [
      "678919fb26d4672f519ac637",
      "medical",
      true,
      false,
      ["HOD"],
      "6791e23e23d7780affd68ffb",
    ],
    COMMUNICATIONS: [
      "678919fb26d4672f519ac638",
      "communications",
      true,
      false,
      ["HOD"],
      "6791e23e23d7780affd68ffc",
    ],
    OHBAT_WAAZ_TALLAQI: [
      "678919fb26d4672f519ac639",
      "ohbat_waaz_tallaqi",
      true,
      false,
      ["HOD"],
      "6791e23e23d7780affd68ffd",
    ],
    ITS: [
      "678919fb26d4672f519ac63a",
      "its",
      true,
      false,
      ["HOD"],
      "6791e23e23d7780affd68ffe",
    ],
  }),
  ITSID_HB: "30351956",
  ITSID_ALI: "30353397",
  AMS_SYSTEMID: "67b6c8a233380165fa037613",
  LADIES_PHONE_VALDIATION: [
    {arazCityID: "67f53a5e4b80130930122e02", name: "Indore", permission: [SUPER_ADMIN, PMO, AAMIL_SAHEB, IPMO, CENTRAL_HR, PMO_DEFAULT]},
    {arazCityID: "682b16ea16b122b53c939018", name: "Chennai", permission: [SUPER_ADMIN, PMO, AAMIL_SAHEB, IPMO, CENTRAL_HR, PMO_DEFAULT]},
    {arazCityID: "682ac243c68aba8919fe7beb", name: "Colombo", permission: [SUPER_ADMIN, PMO, AAMIL_SAHEB, IPMO, CENTRAL_HR, PMO_DEFAULT]},
    {arazCityID: "682436b41b3fe7c2fe4705fb", name: "Dar Es Salaam", permission: [SUPER_ADMIN, PMO, AAMIL_SAHEB, IPMO, CENTRAL_HR, PMO_DEFAULT]},
    {arazCityID: "6815cff63f8aa2c622cb6a77", name: "Nairobi", permission: [SUPER_ADMIN, PMO, AAMIL_SAHEB, IPMO, CENTRAL_HR, PMO_DEFAULT]},
  ]
};
