{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        
        {
            "console": "integratedTerminal",
            "internalConsoleOptions": "neverOpen",
            "name": "nodemon",
            "program": "${workspaceFolder}/server.js",
            "request": "launch",
            "restart": true,
            "runtimeExecutable": "nodemon",
            "skipFiles": [
                "<node_internals>/**"
            ],
            "type": "node"
        },
        {
            "type": "node-terminal",
            "name": "Run Script: server",
            "request": "launch",
            "command": "npm run server",
            "cwd": "${workspaceFolder}"
        },
        {
            "console": "integratedTerminal",
            "internalConsoleOptions": "neverOpen",
            "name": "Auth Service",
            "program": "${workspaceFolder}/modules/authService/app.js",
            "request": "launch",
            "restart": true,
            "runtimeExecutable": "nodemon",
            "skipFiles": [
                "<node_internals>/**"
            ],
            "type": "node"
        }
    ]
}