const express = require("express");
const router = express.Router();
const { validate } = require("../../../middlewares/validation.middleware");
const { authGuard } = require("../../../middlewares/guard.middleware");

// Import controllers
const {
  loginUserV3,
  getUserProfile,
} = require("../controllers/user.controller");

const {
  getEncryptedSystemRole,
} = require("../controllers/systemRole.controller");

const {
  getEncryptedSystemRoleSchema,
} = require("../validations/systemRole.validation");

// ===============================USER ROUTES=======================================================

// Login route (no auth required)
router.post("/its-one-login", loginUserV3);

// Routes that require authentication
router.use(authGuard);

// Profile routes
router.get("/profile", getUserProfile);

router.post(
  "/encrypted-permissions",
  validate(getEncryptedSystemRoleSchema, "body"),
  getEncryptedSystemRole
);

module.exports = router;
