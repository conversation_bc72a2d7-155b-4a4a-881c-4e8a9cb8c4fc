const Joi = require("joi");
const {
  stringValidation,
  booleanValidation,
  numberValidation,
  urlValidation,
  dateValidation,
  arrayObjectValidation,
  idValidation,
  emailValidation,
} = require("../../../../utils/validator.util");

const addHierarchyPositionSchema = Joi.object({
  name: stringValidation,
  alias: stringValidation.optional().allow(""),
  isZonal: booleanValidation.optional(),
  isDepartmental: booleanValidation.optional(),
  isVisibleForArazCityUser: booleanValidation.optional(),
  parentType: stringValidation,
  parents: Joi.array().items(stringValidation.allow("").optional()).optional(),
  weightage: numberValidation,
  countRecommendation: numberValidation,
});

const getSingleHierarchyPositionSchema = Joi.object({
  id: idValidation,
});

const editHierarchyPositionSchema = Joi.object({
  id: idValidation,
  name: stringValidation,
  alias: stringValidation,
  isVisibleForArazCityUser: booleanValidation.optional(),
  weightage: numberValidation,
  countRecommendation: numberValidation,
});

const deleteHierarchyPositionSchema = Joi.object({
  id: idValidation,
});

const saveWeightageAndCountSchema = Joi.object({
  arazCityID: idValidation,
  hierarchyPositionID: idValidation,
  weightage: numberValidation.optional(),
  countRecommendation: numberValidation.optional(),
}).unknown();

module.exports = {
  addHierarchyPositionSchema,
  getSingleHierarchyPositionSchema,
  editHierarchyPositionSchema,
  deleteHierarchyPositionSchema,
  saveWeightageAndCountSchema,
};
