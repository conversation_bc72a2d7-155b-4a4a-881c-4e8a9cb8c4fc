const express = require("express");
const router = express.Router();

const globalMastersRoutes = require("./global/globalMasters/routes");
const hierarchyRoutes = require("./hierarchy/routes");
const communicationRoutes = require("./communication/routes");
const asharaGuideRoutes = require("./global/asharaGuide/routes");
const zonesCapacityRoutes = require("./zonesCapacity/routes");
const taskManagementRoutes = require("./taskManagement/routes");
const accomodationRoutes = require("./global/muqimeenMehmaanMapping/routes");
const documentRoutes = require("./global/documentManager/routes");
const reports = require("./global/reports/routes");
const surveyRoutes = require("./survey/routes");
const analyticsLogRoute = require('./global/analyticsLog/routes');

router.use("/global-master", globalMastersRoutes);
router.use("/hierarchy", hierarchyRoutes);
router.use("/communication", communicationRoutes);
router.use("/ashara-guide", asharaGuideRoutes);
router.use("/zones-capacity", zonesCapacityRoutes);
router.use("/task-management", taskManagementRoutes);
router.use("/muqimeen-mehmaan-mapping", accomodationRoutes);
router.use("/document-manager", documentRoutes);
router.use("/report", reports);
router.use("/survey", surveyRoutes);
router.use('/activity', analyticsLogRoute);

// will be removed in future 
const hrRoutes = require("./hierarchy/routes/interest.route");
router.use("/hr/interest", hrRoutes);

module.exports = router;
