const Joi = require("joi");
const {
  stringValidation,
  booleanValidation,
  numberValidation,
  urlValidation,
  dateValidation,
  arrayObjectValidation,
  idValidation,
  emailValidation,
} = require("../../../../utils/validator.util");

const addFunctionSchema = Joi.object({
  name: stringValidation,
}).unknown();

const getSingleFunctionSchema = Joi.object({
  id: idValidation,
});

const editFunctionSchema = Joi.object({
  id: idValidation,
  name: stringValidation,
}).unknown();

const deleteFunctionSchema = Joi.object({
  id: idValidation,
});

const getFunctionsByDepartmentSchema = Joi.object({
  id: idValidation,
});

module.exports = {
  addFunctionSchema,
  getSingleFunctionSchema,
  editFunctionSchema,
  deleteFunctionSchema,
  getFunctionsByDepartmentSchema,
};
