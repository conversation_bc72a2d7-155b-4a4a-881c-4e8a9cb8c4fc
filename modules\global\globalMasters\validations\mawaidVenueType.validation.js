const Joi = require("joi");
const {
  stringValidation,
  idValidation,
} = require("../../../../utils/validator.util");

const addMawaidVenueTypeSchema = Joi.object({
  name: stringValidation,
});

const getSingleMawaidVenueTypeSchema = Joi.object({
  id: idValidation,
});

const editMawaidVenueTypeSchema = Joi.object({
  id: idValidation,
  name: stringValidation,
});

const deleteMawaidVenueTypeSchema = Joi.object({
  id: idValidation,
});

module.exports = {
  addMawaidVenueTypeSchema,
  getSingleMawaidVenueTypeSchema,
  editMawaidVenueTypeSchema,
  deleteMawaidVenueTypeSchema,
};
