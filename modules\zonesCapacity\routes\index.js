const express = require("express");
const router = express.Router();

const healthcheckRoute = require("./healthcheck.route");
const waazVenueTypeRoute = require("../../global/globalMasters/routes/waazVenueType.route");
const waazVenueSuitabilityRoute = require("../../global/globalMasters/routes//waazVenueSuitability.route");
const waazVenuePriorityRoute = require("../../global/globalMasters/routes//waazVenuePriority.route");
const mawaidVenueTypeRoute = require("../../global/globalMasters/routes//mawaidVenueType.route");
const mawaidVenueSuitabilityRoute = require("../../global/globalMasters/routes//mawaidVenueSuitability.route");
const waazVenueRoute = require("./waazVenue.route");
const globalFunction = require("./globalFunction.route");
const waazSeatingCapacities = require("../../global/globalMasters/routes//waazSeatingCapacity.route");
const zoneMapping = require("./zoneMapping.route");
const mawaidVenueRoute = require("./mawaidVenue.route");
const dashboard = require("./dashboard.route");
const arazCityZoneRoute = require("../../hierarchy/routes/arazCityZone.route"); 
const razaMappingRoute = require("./razaMapping.route")

router.use("/healthcheck", healthcheckRoute);
router.use("/waaz-venue", waazVenueRoute);
router.use("/mawaid-venue", mawaidVenueRoute);
router.use("/global-function", globalFunction);
router.use("/zone-mapping", zoneMapping);
router.use("/dashboard", dashboard);
router.use("/araz-city-zone", arazCityZoneRoute);
router.use("/raza-mapping", razaMappingRoute);


//to be removed in future
router.use("/waaz-venue-priority", waazVenuePriorityRoute);
router.use("/waaz-venue-type", waazVenueTypeRoute);
router.use("/waaz-venue-suitability", waazVenueSuitabilityRoute);
router.use("/mawaid-venue-type", mawaidVenueTypeRoute);
router.use("/mawaid-venue-suitability", mawaidVenueSuitabilityRoute);
router.use("/waaz-seating-capacity", waazSeatingCapacities);

module.exports = router;
